-- 职业病总检专用组件菜单SQL语句
-- 执行前请根据实际情况调整菜单ID、父级菜单ID等参数

-- 1. 插入职业病总检主菜单
INSERT INTO sys_permission (
    id, 
    parent_id, 
    name, 
    url, 
    component, 
    component_name, 
    redirect, 
    menu_type, 
    perms, 
    perms_type, 
    sort_no, 
    always_show, 
    icon, 
    is_route, 
    is_leaf, 
    keep_alive, 
    hidden, 
    hide_tab, 
    description, 
    status, 
    del_flag, 
    rule_flag, 
    create_by, 
    create_time, 
    update_by, 
    update_time, 
    internal_or_external
) VALUES (
    '1856217897316001001',  -- 菜单ID，请根据实际情况调整
    null,  -- 父级菜单ID，总检管理的父级菜单
    '职业病总检',
    '/summary/occu-summary',
    'views/summary/SummaryPannel4Occu',
    'SummaryPannel4Occu',
    NULL,
    0,  -- 菜单类型：0-菜单，1-按钮
    NULL,
    '1',  -- 权限类型：1-菜单权限
    1.0,  -- 排序号
    0,    -- 是否总是显示
    'ant-design:medicine-box-outlined',  -- 图标
    1,    -- 是否路由菜单
    1,    -- 是否叶子节点
    1,    -- 是否缓存
    0,    -- 是否隐藏
    0,    -- 是否隐藏Tab
    '职业病总检专用组件，支持扫码枪快速检索',
    '1',  -- 状态：1-正常
    0,    -- 删除标志：0-正常
    0,    -- 规则标志
    'admin',
    NOW(),
    'admin',
    NOW(),
    0     -- 是否外部链接：0-否
);

-- 2. 插入职业病总检相关按钮权限
-- 2.1 新增职业检结论权限
INSERT INTO sys_permission (
    id, 
    parent_id, 
    name, 
    url, 
    component, 
    component_name, 
    redirect, 
    menu_type, 
    perms, 
    perms_type, 
    sort_no, 
    always_show, 
    icon, 
    is_route, 
    is_leaf, 
    keep_alive, 
    hidden, 
    hide_tab, 
    description, 
    status, 
    del_flag, 
    rule_flag, 
    create_by, 
    create_time, 
    update_by, 
    update_time, 
    internal_or_external
) VALUES (
    '1856217897316001002',
    '1856217897316001001',  -- 父级为职业病总检菜单
    '新增结论',
    NULL,
    NULL,
    NULL,
    NULL,
    1,  -- 按钮类型
    'summary:occu_conclusion:add',
    '2',  -- 按钮权限
    1.0,
    0,
    NULL,
    0,
    1,
    0,
    0,
    0,
    '新增职业检结论权限',
    '1',
    0,
    0,
    'admin',
    NOW(),
    'admin',
    NOW(),
    0
);

-- 2.2 编辑职业检结论权限
INSERT INTO sys_permission (
    id, parent_id, name, url, component, component_name, redirect, menu_type, 
    perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, 
    keep_alive, hidden, hide_tab, description, status, del_flag, rule_flag, 
    create_by, create_time, update_by, update_time, internal_or_external
) VALUES (
    '1856217897316001003', '1856217897316001001', '编辑结论', NULL, NULL, NULL, NULL, 1,
    'summary:occu_conclusion:edit', '2', 2.0, 0, NULL, 0, 1,
    0, 0, 0, '编辑职业检结论权限', '1', 0, 0,
    'admin', NOW(), 'admin', NOW(), 0
);

-- 2.3 删除职业检结论权限
INSERT INTO sys_permission (
    id, parent_id, name, url, component, component_name, redirect, menu_type, 
    perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, 
    keep_alive, hidden, hide_tab, description, status, del_flag, rule_flag, 
    create_by, create_time, update_by, update_time, internal_or_external
) VALUES (
    '1856217897316001004', '1856217897316001001', '删除结论', NULL, NULL, NULL, NULL, 1,
    'summary:occu_conclusion:delete', '2', 3.0, 0, NULL, 0, 1,
    0, 0, 0, '删除职业检结论权限', '1', 0, 0,
    'admin', NOW(), 'admin', NOW(), 0
);

-- 2.4 批量操作权限
INSERT INTO sys_permission (
    id, parent_id, name, url, component, component_name, redirect, menu_type, 
    perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, 
    keep_alive, hidden, hide_tab, description, status, del_flag, rule_flag, 
    create_by, create_time, update_by, update_time, internal_or_external
) VALUES (
    '1856217897316001005', '1856217897316001001', '批量操作', NULL, NULL, NULL, NULL, 1,
    'summary:occu_conclusion:batch', '2', 4.0, 0, NULL, 0, 1,
    0, 0, 0, '职业检结论批量操作权限', '1', 0, 0,
    'admin', NOW(), 'admin', NOW(), 0
);

-- 2.5 导出权限
INSERT INTO sys_permission (
    id, parent_id, name, url, component, component_name, redirect, menu_type, 
    perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, 
    keep_alive, hidden, hide_tab, description, status, del_flag, rule_flag, 
    create_by, create_time, update_by, update_time, internal_or_external
) VALUES (
    '1856217897316001006', '1856217897316001001', '导出数据', NULL, NULL, NULL, NULL, 1,
    'summary:occu_conclusion:export', '2', 5.0, 0, NULL, 0, 1,
    0, 0, 0, '职业检结论导出权限', '1', 0, 0,
    'admin', NOW(), 'admin', NOW(), 0
);

-- 2.6 审核权限
INSERT INTO sys_permission (
    id, parent_id, name, url, component, component_name, redirect, menu_type, 
    perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, 
    keep_alive, hidden, hide_tab, description, status, del_flag, rule_flag, 
    create_by, create_time, update_by, update_time, internal_or_external
) VALUES (
    '1856217897316001007', '1856217897316001001', '审核结论', NULL, NULL, NULL, NULL, 1,
    'summary:occu_conclusion:audit', '2', 6.0, 0, NULL, 0, 1,
    0, 0, 0, '职业检结论审核权限', '1', 0, 0,
    'admin', NOW(), 'admin', NOW(), 0
);

-- 2.7 打印报告权限
INSERT INTO sys_permission (
    id, parent_id, name, url, component, component_name, redirect, menu_type, 
    perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, 
    keep_alive, hidden, hide_tab, description, status, del_flag, rule_flag, 
    create_by, create_time, update_by, update_time, internal_or_external
) VALUES (
    '1856217897316001008', '1856217897316001001', '打印报告', NULL, NULL, NULL, NULL, 1,
    'summary:occu_report:print', '2', 7.0, 0, NULL, 0, 1,
    0, 0, 0, '职业检报告打印权限', '1', 0, 0,
    'admin', NOW(), 'admin', NOW(), 0
);

-- 3. 为管理员角色分配权限（假设管理员角色ID为'1'）
-- 请根据实际的角色ID进行调整
INSERT INTO sys_role_permission (id, role_id, permission_id) VALUES 
('1856217897316002001', '1', '1856217897316001001'),  -- 职业病总检菜单
('1856217897316002002', '1', '1856217897316001002'),  -- 新增结论
('1856217897316002003', '1', '1856217897316001003'),  -- 编辑结论
('1856217897316002004', '1', '1856217897316001004'),  -- 删除结论
('1856217897316002005', '1', '1856217897316001005'),  -- 批量操作
('1856217897316002006', '1', '1856217897316001006'),  -- 导出数据
('1856217897316002007', '1', '1856217897316001007'),  -- 审核结论
('1856217897316002008', '1', '1856217897316001008');  -- 打印报告

-- 4. 创建职业检医生角色（可选）
INSERT INTO sys_role (
    id, 
    role_name, 
    role_code, 
    description, 
    create_by, 
    create_time, 
    update_by, 
    update_time, 
    status
) VALUES (
    '1856217897316003001',
    '职业检医生',
    'OCCU_DOCTOR',
    '职业检医生角色，具有职业病总检相关权限',
    'admin',
    NOW(),
    'admin',
    NOW(),
    1
);

-- 5. 为职业检医生角色分配权限
INSERT INTO sys_role_permission (id, role_id, permission_id) VALUES 
('1856217897316004001', '1856217897316003001', '1856217897316001001'),  -- 职业病总检菜单
('1856217897316004002', '1856217897316003001', '1856217897316001002'),  -- 新增结论
('1856217897316004003', '1856217897316003001', '1856217897316001003'),  -- 编辑结论
('1856217897316004004', '1856217897316003001', '1856217897316001006'),  -- 导出数据
('1856217897316004005', '1856217897316003001', '1856217897316001008');  -- 打印报告

-- 6. 创建职业检主任角色（可选）
INSERT INTO sys_role (
    id, role_name, role_code, description, create_by, create_time, update_by, update_time, status
) VALUES (
    '1856217897316003002', '职业检主任', 'OCCU_DIRECTOR', 
    '职业检主任角色，具有职业病总检所有权限包括审核', 
    'admin', NOW(), 'admin', NOW(), 1
);

-- 7. 为职业检主任角色分配所有权限
INSERT INTO sys_role_permission (id, role_id, permission_id) VALUES 
('1856217897316005001', '1856217897316003002', '1856217897316001001'),  -- 职业病总检菜单
('1856217897316005002', '1856217897316003002', '1856217897316001002'),  -- 新增结论
('1856217897316005003', '1856217897316003002', '1856217897316001003'),  -- 编辑结论
('1856217897316005004', '1856217897316003002', '1856217897316001004'),  -- 删除结论
('1856217897316005005', '1856217897316003002', '1856217897316001005'),  -- 批量操作
('1856217897316005006', '1856217897316003002', '1856217897316001006'),  -- 导出数据
('1856217897316005007', '1856217897316003002', '1856217897316001007'),  -- 审核结论
('1856217897316005008', '1856217897316003002', '1856217897316001008');  -- 打印报告

-- 执行完成后的验证SQL
-- 验证菜单是否插入成功
SELECT * FROM sys_permission WHERE name = '职业病总检';

-- 验证权限是否分配成功
SELECT sp.name, sr.role_name 
FROM sys_permission sp 
JOIN sys_role_permission srp ON sp.id = srp.permission_id 
JOIN sys_role sr ON srp.role_id = sr.id 
WHERE sp.parent_id = '1856217897316001001' OR sp.id = '1856217897316001001';

-- 注意事项：
-- 1. 请根据实际的数据库表结构调整字段名称
-- 2. 请根据实际的ID生成规则调整菜单ID
-- 3. 请根据实际的父级菜单ID调整parent_id
-- 4. 请根据实际的角色ID调整角色权限分配
-- 5. 执行前请备份数据库
