/**
 * 菜单助手工具 - 增强容错性版本
 * 解决 "Cannot read properties of null (reading 'startsWith')" 错误
 */

import type { Menu } from '/@/router/types';
import type { MenuModule } from '/@/router/types';

/**
 * 安全的路径拼接函数 - 增强容错性
 * @param parentPath 父级路径
 * @param path 当前路径
 * @returns 拼接后的路径
 */
export function safeJoinParentPath(parentPath: string | null | undefined, path: string | null | undefined): string {
  try {
    // 处理空值情况
    if (!path || path === 'null' || path === 'undefined') {
      return parentPath || '/';
    }
    
    if (!parentPath || parentPath === 'null' || parentPath === 'undefined' || parentPath === '0') {
      return path.startsWith('/') ? path : `/${path}`;
    }

    // 确保路径以 / 开头
    const safePath = path.startsWith('/') ? path : `/${path}`;
    const safeParentPath = parentPath.startsWith('/') ? parentPath : `/${parentPath}`;
    
    // 避免重复的斜杠
    if (safeParentPath.endsWith('/')) {
      return `${safeParentPath.slice(0, -1)}${safePath}`;
    }
    
    return `${safeParentPath}${safePath}`;
  } catch (error) {
    console.warn('路径拼接失败，使用默认路径:', { parentPath, path, error });
    return path || parentPath || '/';
  }
}

/**
 * 安全的菜单转换函数
 * @param menu 菜单项
 * @param parentPath 父级路径
 * @returns 转换后的菜单
 */
export function safeTransformRouteToMenu(menu: any, parentPath?: string): Menu {
  try {
    const { 
      name, 
      url, 
      component, 
      redirect, 
      meta = {}, 
      children,
      parent_id,
      ...rest 
    } = menu;

    // 安全处理路径
    const safePath = safeJoinParentPath(parentPath, url);
    
    // 构建菜单项
    const menuItem: Menu = {
      ...rest,
      name: name || 'UnknownMenu',
      path: safePath,
      component: component || undefined,
      redirect: redirect || undefined,
      meta: {
        title: name || 'Unknown',
        icon: meta.icon || 'ant-design:menu-outlined',
        hideMenu: meta.hideMenu || false,
        hideBreadcrumb: meta.hideBreadcrumb || false,
        hideTab: meta.hideTab || false,
        carryParam: meta.carryParam || false,
        hideChildrenInMenu: meta.hideChildrenInMenu || false,
        affix: meta.affix || false,
        dynamicLevel: meta.dynamicLevel || undefined,
        realPath: meta.realPath || undefined,
        ...meta,
      },
    };

    // 递归处理子菜单
    if (children && Array.isArray(children) && children.length > 0) {
      menuItem.children = children.map(child => 
        safeTransformRouteToMenu(child, safePath)
      ).filter(Boolean); // 过滤掉转换失败的项
    }

    return menuItem;
  } catch (error) {
    console.error('菜单转换失败:', { menu, parentPath, error });
    
    // 返回一个安全的默认菜单项
    return {
      name: 'ErrorMenu',
      path: '/error',
      meta: {
        title: '菜单错误',
        icon: 'ant-design:exclamation-circle-outlined',
        hideMenu: true,
      },
    };
  }
}

/**
 * 验证菜单数据的完整性
 * @param menuData 菜单数据
 * @returns 验证结果
 */
export function validateMenuData(menuData: any[]): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!Array.isArray(menuData)) {
    errors.push('菜单数据必须是数组');
    return { valid: false, errors };
  }

  menuData.forEach((menu, index) => {
    if (!menu) {
      errors.push(`菜单项 ${index} 为空`);
      return;
    }

    if (!menu.name) {
      errors.push(`菜单项 ${index} 缺少 name 字段`);
    }

    if (!menu.url && !menu.redirect) {
      errors.push(`菜单项 ${index} 缺少 url 或 redirect 字段`);
    }

    // 检查 parent_id 是否为 null（这是导致错误的主要原因）
    if (menu.parent_id === null) {
      console.warn(`菜单项 ${menu.name} 的 parent_id 为 null，可能导致路径拼接错误`);
    }
  });

  return { valid: errors.length === 0, errors };
}

/**
 * 修复菜单数据中的常见问题
 * @param menuData 原始菜单数据
 * @returns 修复后的菜单数据
 */
export function fixMenuData(menuData: any[]): any[] {
  if (!Array.isArray(menuData)) {
    console.error('菜单数据不是数组，返回空数组');
    return [];
  }

  return menuData.map(menu => {
    if (!menu) return null;

    const fixedMenu = { ...menu };

    // 修复 parent_id 为 null 的问题
    if (fixedMenu.parent_id === null || fixedMenu.parent_id === 'null') {
      fixedMenu.parent_id = '0';
      console.warn(`已修复菜单 ${menu.name} 的 parent_id 从 null 改为 '0'`);
    }

    // 确保必要字段存在
    if (!fixedMenu.name) {
      fixedMenu.name = `Menu_${Date.now()}`;
    }

    if (!fixedMenu.url && !fixedMenu.redirect) {
      fixedMenu.url = '/dashboard';
      console.warn(`菜单 ${fixedMenu.name} 缺少路径，设置默认路径`);
    }

    // 确保路径格式正确
    if (fixedMenu.url && !fixedMenu.url.startsWith('/')) {
      fixedMenu.url = `/${fixedMenu.url}`;
    }

    return fixedMenu;
  }).filter(Boolean); // 过滤掉空值
}

/**
 * 安全的菜单构建函数
 * @param menuData 菜单数据
 * @returns 构建后的菜单树
 */
export function safeBuildMenuTree(menuData: any[]): Menu[] {
  try {
    // 验证和修复菜单数据
    const { valid, errors } = validateMenuData(menuData);
    if (!valid) {
      console.error('菜单数据验证失败:', errors);
    }

    const fixedMenuData = fixMenuData(menuData);
    
    // 构建菜单树
    const menuMap = new Map<string, any>();
    const rootMenus: any[] = [];

    // 第一遍：创建所有菜单项的映射
    fixedMenuData.forEach(menu => {
      menuMap.set(menu.id, { ...menu, children: [] });
    });

    // 第二遍：构建父子关系
    fixedMenuData.forEach(menu => {
      const menuItem = menuMap.get(menu.id);
      if (!menuItem) return;

      const parentId = menu.parent_id;
      if (!parentId || parentId === '0' || parentId === 'null') {
        // 顶级菜单
        rootMenus.push(menuItem);
      } else {
        // 子菜单
        const parent = menuMap.get(parentId);
        if (parent) {
          parent.children.push(menuItem);
        } else {
          console.warn(`找不到父菜单 ${parentId}，将 ${menu.name} 作为顶级菜单`);
          rootMenus.push(menuItem);
        }
      }
    });

    // 转换为标准菜单格式
    return rootMenus.map(menu => safeTransformRouteToMenu(menu));
  } catch (error) {
    console.error('菜单构建失败:', error);
    return [];
  }
}

/**
 * 菜单错误恢复函数
 * @param error 错误对象
 * @param menuData 原始菜单数据
 * @returns 恢复后的菜单数据
 */
export function recoverFromMenuError(error: Error, menuData: any[]): Menu[] {
  console.error('菜单处理出现错误，尝试恢复:', error);
  
  try {
    // 尝试使用安全的菜单构建函数
    return safeBuildMenuTree(menuData);
  } catch (recoveryError) {
    console.error('菜单恢复失败，返回默认菜单:', recoveryError);
    
    // 返回一个最基本的菜单结构
    return [
      {
        name: 'Dashboard',
        path: '/dashboard',
        meta: {
          title: '首页',
          icon: 'ant-design:home-outlined',
        },
      },
      {
        name: 'OccuSummary',
        path: '/summary/occu-summary',
        component: 'views/summary/SummaryPannel4Occu',
        meta: {
          title: '职业病总检',
          icon: 'ant-design:medicine-box-outlined',
        },
      },
    ];
  }
}
