/**
 * 菜单初始化和修复工具
 * 在应用启动时自动检测和修复菜单问题
 */

import { App } from 'vue';
import { Router } from 'vue-router';
import { createMenuErrorGuard } from '/@/router/guard/menuErrorGuard';
import { fixMenuData, validateMenuData } from '/@/utils/menuHelperFix';

/**
 * 菜单修复配置
 */
interface MenuFixConfig {
  autoFix: boolean;           // 是否自动修复
  showDiagnostic: boolean;    // 是否显示诊断信息
  enableMonitor: boolean;     // 是否启用监控
  logLevel: 'error' | 'warn' | 'info' | 'debug';
}

const defaultConfig: MenuFixConfig = {
  autoFix: true,
  showDiagnostic: import.meta.env.DEV, // 开发环境显示诊断
  enableMonitor: true,
  logLevel: import.meta.env.DEV ? 'debug' : 'warn',
};

/**
 * 日志工具
 */
class MenuLogger {
  private level: string;
  
  constructor(level: string) {
    this.level = level;
  }
  
  private shouldLog(level: string): boolean {
    const levels = ['error', 'warn', 'info', 'debug'];
    return levels.indexOf(level) <= levels.indexOf(this.level);
  }
  
  error(...args: any[]) {
    if (this.shouldLog('error')) {
      console.error('[MenuFix]', ...args);
    }
  }
  
  warn(...args: any[]) {
    if (this.shouldLog('warn')) {
      console.warn('[MenuFix]', ...args);
    }
  }
  
  info(...args: any[]) {
    if (this.shouldLog('info')) {
      console.info('[MenuFix]', ...args);
    }
  }
  
  debug(...args: any[]) {
    if (this.shouldLog('debug')) {
      console.debug('[MenuFix]', ...args);
    }
  }
}

let logger: MenuLogger;
let config: MenuFixConfig;

/**
 * 初始化菜单修复系统
 */
export function initMenuFix(app: App, router: Router, userConfig?: Partial<MenuFixConfig>) {
  config = { ...defaultConfig, ...userConfig };
  logger = new MenuLogger(config.logLevel);
  
  logger.info('初始化菜单修复系统', config);
  
  // 创建菜单错误守卫
  if (config.enableMonitor) {
    const menuGuard = createMenuErrorGuard(router);
    logger.debug('菜单错误守卫已创建');
    
    // 在开发环境中添加全局菜单修复工具
    if (config.showDiagnostic && import.meta.env.DEV) {
      addGlobalMenuTools(menuGuard);
    }
  }
  
  // 监听路由错误
  router.onError((error) => {
    if (isMenuError(error)) {
      logger.error('路由错误（菜单相关）:', error.message);
      if (config.autoFix) {
        handleMenuError(error);
      }
    }
  });
  
  // 全局错误处理
  app.config.errorHandler = (error, instance, info) => {
    if (isMenuError(error)) {
      logger.error('Vue错误（菜单相关）:', error.message, info);
      if (config.autoFix) {
        handleMenuError(error);
      }
    }
  };
  
  logger.info('菜单修复系统初始化完成');
}

/**
 * 判断是否是菜单相关错误
 */
function isMenuError(error: any): boolean {
  if (!error || !error.message) return false;
  
  const menuErrorPatterns = [
    /Cannot read properties of null.*reading 'startsWith'/,
    /menuHelper/i,
    /joinParentPath/i,
    /transformRouteToMenu/i,
    /buildRoutesAction/i,
    /parent_id.*null/i,
  ];
  
  return menuErrorPatterns.some(pattern => pattern.test(error.message));
}

/**
 * 处理菜单错误
 */
async function handleMenuError(error: any) {
  logger.warn('检测到菜单错误，开始自动修复:', error.message);
  
  try {
    // 动态导入权限存储（避免循环依赖）
    const { usePermissionStore } = await import('/@/store/modules/permission');
    const permissionStore = usePermissionStore();
    
    // 获取当前菜单数据
    const menuData = permissionStore.getBackMenuList || [];
    
    if (menuData.length === 0) {
      logger.warn('菜单数据为空，跳过修复');
      return;
    }
    
    // 验证菜单数据
    const { valid, errors } = validateMenuData(menuData);
    
    if (!valid) {
      logger.warn('菜单数据验证失败:', errors);
      
      // 修复菜单数据
      const fixedMenuData = fixMenuData(menuData);
      logger.info('菜单数据已修复，修复项数:', fixedMenuData.length);
      
      // 重新构建路由
      await permissionStore.buildRoutesAction();
      logger.info('路由重新构建完成');
    }
    
  } catch (fixError) {
    logger.error('菜单自动修复失败:', fixError);
  }
}

/**
 * 添加全局菜单工具（开发环境）
 */
function addGlobalMenuTools(menuGuard: any) {
  // 添加到全局对象，方便调试
  (window as any).__menuFix = {
    checkHealth: menuGuard.checkMenuHealth,
    fixMenus: menuGuard.handleMenuError,
    config,
    logger,
    
    // 快速修复命令
    quickFix() {
      logger.info('执行快速修复...');
      return menuGuard.handleMenuError(new Error('Manual fix triggered'));
    },
    
    // 显示诊断信息
    diagnose() {
      const result = menuGuard.checkMenuHealth();
      console.table(result);
      return result;
    },
    
    // 重置配置
    setConfig(newConfig: Partial<MenuFixConfig>) {
      Object.assign(config, newConfig);
      logger.info('配置已更新:', config);
    },
  };
  
  logger.debug('全局菜单工具已添加到 window.__menuFix');
  
  // 在控制台显示帮助信息
  console.log(`
%c🛠️ 菜单修复工具已启用
%c可用命令:
  __menuFix.quickFix()    - 快速修复菜单
  __menuFix.diagnose()    - 诊断菜单问题
  __menuFix.checkHealth() - 检查菜单健康状态
  __menuFix.setConfig()   - 更新配置
`, 
    'color: #1890ff; font-weight: bold; font-size: 14px;',
    'color: #666; font-size: 12px;'
  );
}

/**
 * 菜单预检查（在应用启动前调用）
 */
export async function preCheckMenus(): Promise<boolean> {
  try {
    logger.info('开始菜单预检查...');
    
    // 动态导入权限存储
    const { usePermissionStore } = await import('/@/store/modules/permission');
    const permissionStore = usePermissionStore();
    
    // 获取菜单数据
    const menuData = permissionStore.getBackMenuList || [];
    
    if (menuData.length === 0) {
      logger.warn('菜单数据为空，预检查通过');
      return true;
    }
    
    // 验证菜单数据
    const { valid, errors } = validateMenuData(menuData);
    
    if (valid) {
      logger.info('菜单预检查通过');
      return true;
    } else {
      logger.warn('菜单预检查发现问题:', errors);
      
      if (config.autoFix) {
        // 自动修复
        const fixedMenuData = fixMenuData(menuData);
        logger.info('菜单已自动修复');
        return true;
      } else {
        logger.error('菜单存在问题且未启用自动修复');
        return false;
      }
    }
    
  } catch (error) {
    logger.error('菜单预检查失败:', error);
    return false;
  }
}

/**
 * 创建菜单修复中间件
 */
export function createMenuFixMiddleware() {
  return async (to: any, from: any, next: any) => {
    try {
      // 在每次路由跳转前检查菜单状态
      const isHealthy = await preCheckMenus();
      
      if (isHealthy) {
        next();
      } else {
        logger.error('菜单状态异常，阻止路由跳转');
        next(new Error('Menu system error'));
      }
    } catch (error) {
      logger.error('菜单中间件执行失败:', error);
      next(error);
    }
  };
}

/**
 * 菜单修复状态
 */
export const menuFixStatus = {
  get isEnabled() {
    return !!config;
  },
  
  get config() {
    return config;
  },
  
  get logger() {
    return logger;
  },
};
