<template>
  <div>
    <!-- 扫码专用输入区域 -->
    <div class="scan-input-section">
      <a-card size="small" class="scan-card">
        <template #title>
          <div class="scan-title">
            <span class="scan-icon">📱</span>
            <span>扫码枪输入</span>
            <a-tag v-if="scanStatus.isScanning" color="processing" class="scan-status-tag">
              <a-spin size="small" />
              扫码中...
            </a-tag>
            <a-tag v-else color="default" class="scan-status-tag">待机</a-tag>
          </div>
        </template>
        <div class="scan-input-wrapper">
          <a-input
            ref="scanInput"
            v-model:value="scanInputValue"
            placeholder="请使用扫码枪扫描体检号，或手动输入后按回车"
            size="large"
            :class="{ 'scan-input-active': scanStatus.isScanning }"
            @input="handleScanInput"
            @keyup.enter="handleManualSearch"
            @focus="handleScanInputFocus"
            @blur="handleScanInputBlur"
          >
            <template #prefix>
              <span class="scan-prefix-icon">🔍</span>
            </template>
            <template #suffix>
              <a-button
                v-if="scanInputValue"
                type="text"
                size="small"
                @click="clearScanInput"
                class="clear-btn"
              >
                ✕
              </a-button>
            </template>
          </a-input>
          <div v-if="scanStatus.message" class="scan-message">
            {{ scanStatus.message }}
          </div>
        </div>
      </a-card>
    </div>

    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="4">
          <a-col :span="4">
            <a-form-item name="examNo" label="体检号">
              <a-input
                ref="examNoInput"
                allow-clear
                size="middle"
                placeholder="请输入体检号或使用扫码枪扫描"
                v-model:value="queryParam.examNo"
                @input="handleExamNoInput"
                :class="{ 'scan-input-active': scanStatus.isScanning }"
              />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item name="name" label="姓名">
              <a-input allow-clear size="middle" placeholder="请输入姓名" v-model:value="queryParam.name" />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item name="idCard" label="证件号">
              <a-input allow-clear size="middle" placeholder="请输入证件号" v-model:value="queryParam.idCard" />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="总检状态">
              <j-dict-select-tag dict-code="summary_status" size="middle" placeholder="总检状态" v-model:value="queryParam.summaryStatus" />
            </a-form-item>
          </a-col>

          <!--          <a-col :lg="12">
            <a-form-item name="status">
              <j-dict-select-tag dict-code="checkStatus" size="middle" placeholder="检查状态" v-model:value="queryParam.checkState" />
            </a-form-item>
          </a-col>-->

          <template v-if="toggleSearchStatus">
            <a-col :span="4">
              <a-form-item label="报告状态">
                <j-dict-select-tag dict-code="report_print_status" size="middle" placeholder="报告状态" v-model:value="queryParam.printStatus" />
              </a-form-item>
            </a-col>

            <a-col :span="6">
              <a-form-item label="医生">
                <a-input-group compact>
                  <j-async-search-select
                    placeholder="请选择医生"
                    dict="sys_user where del_flag=0,realname,username"
                    v-model:value="queryParam.doctor"
                    :allow-clear="true"
                    style="width: 60%"
                  />
                  <a-select v-model:value="queryParam.doctorType" placeholder="医生类型" style="width: 40%">
                    <a-select-option value="">无</a-select-option>
                    <a-select-option value="初检">初检</a-select-option>
                    <a-select-option value="主检">主检</a-select-option>
                    <a-select-option value="审核">审核</a-select-option>
                    <a-select-option value="指定的主检">指定的主检</a-select-option>
                  </a-select>
                </a-input-group>
              </a-form-item>
            </a-col>

            <a-col :span="4">
              <a-form-item label="日期类型">
                <a-select v-model:value="queryParam.dateType" placeholder="日期类型" style="width: 100%">
                  <a-select-option value="登记日期">登记日期</a-select-option>
                  <a-select-option value="初检日期">初检日期</a-select-option>
                  <a-select-option value="主检日期">主检日期</a-select-option>
                  <a-select-option value="审核日期">审核日期</a-select-option>
                  <a-select-option value="打印日期">打印日期</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="4">
              <a-form-item name="itemId" label="排序">
                <a-select v-model:value="queryParam.sortOrder" placeholder="择排序方式">
                  <template #suffixIcon>
                    <SortAscendingOutlined />
                  </template>
                  <a-select-option value="降序">降序</a-select-option>
                  <a-select-option value="升序">升序</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :span="6">
              <a-form-item label="日期范围">
                <a-range-picker
                  v-model:value="regDateRange"
                  placement="日期范围"
                  @change="searchQuery"
                  :presets="rangePresets"
                  :allow-clear="false"
                />
              </a-form-item>
            </a-col>
            <a-col :span="4">
              <a-form-item name="itemId" label="积案查询">
                <a-input-number v-model:value="queryParam.daysFromReg" placeholder="积案天数" />
              </a-form-item>
            </a-col>

            <a-col :span="4">
              <a-form-item name="itemId" label="过滤">
                <a-select v-model:value="queryParam.filterStatus" placeholder="数据过滤">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="已交表">已交表</a-select-option>
                  <a-select-option value="已预检">已预检</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <!--<a-row :gutter="4">
            <a-col :span="24">
              <a-form-item label="日期范围">
                <a-range-picker
                  v-model:value="regDateRange"
                  placement="日期类型"
                  @change="searchQuery"
                  :presets="rangePresets"
                  :allow-clear="false"
                />
              </a-form-item>
            </a-col>
          </a-row>-->
            <a-col :span="4">
              <a-form-item label="预检方式">
                <j-dict-select-tag dict-code="pre_summary_method" size="middle" placeholder="预检方式" v-model:value="queryParam.preSummaryMethod" />
              </a-form-item>
            </a-col>
            <a-col :span="4">
              <a-form-item label="初检方式">
                <j-dict-select-tag
                  dict-code="initail_summary_method"
                  size="middle"
                  placeholder="初检方式"
                  v-model:value="queryParam.initailSummaryMethod"
                />
              </a-form-item>
            </a-col>
            <a-col :lg="4">
              <a-form-item label="所属预约">
                <j-async-search-select
                  size="middle"
                  placeholder="所属预约"
                  @change="getTeamList"
                  v-model:value="queryParam.companyRegId"
                  dict="company_reg,reg_name,id"
                  :allow-clear="true"
                />
              </a-form-item>
            </a-col>
            <a-col :lg="4">
              <a-form-item label="体检类型">
                <j-dict-select-tag dict-code="examination_type" size="middle" placeholder="体检类型" v-model:value="queryParam.examCatory" />
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="6" :md="6" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button size="middle" type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
              <a-button size="middle" type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px"
                >重置</a-button
              >
              <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="null">
      <!--插槽:table标题-->
      <template #bodyCell="{ column, text, record, index }">
        <template v-if="column.dataIndex == 'initailSummaryMethod'">
          <a-tag :bordered="false" color="green" v-if="text == 'AI'"> {{ text }}</a-tag>
          <a-tag :bordered="false" color="blue" v-if="text == '手动'"> {{ text }}</a-tag>
        </template>
        <template v-if="column.dataIndex == 'preSummaryMethod'">
          <a-tag :bordered="false" color="green" v-if="text == '自动'"> {{ text }}</a-tag>
          <a-tag :bordered="false" color="blue" v-if="text == '手动'"> {{ text }}</a-tag>
        </template>
        <template v-if="column.dataIndex == 'summaryStatus'">
          <a-tag :bordered="false" :color="record.summaryStatusColor"> {{ record.summaryStatus }}</a-tag>
        </template>
        <template v-else-if="column.dataIndex == 'checkStatus'">
          <a-space>
            <template v-for="status in record.statusStatList">
              <a-popover :title="status.status + ':' + status.count + '项'" trigger="click">
                <template #content>
                  <div style="width: 20vw">
                    <a-table
                      :rowKey="(_, index) => index"
                      :dataSource="status.items"
                      :showHeader="false"
                      size="small"
                      :pagination="false"
                      :scroll="{ y: 200 }"
                    >
                      <a-table-column key="item">
                        <!-- Use the default slot to render each string -->
                        <template #default="{ record, index }"> {{ index + 1 }}、 {{ record }}</template>
                      </a-table-column>
                    </a-table>
                  </div>
                </template>
                <a-tag :bordered="false" :color="status.color" style="cursor: pointer">{{ status.status }}({{ status.count }}项) </a-tag>
              </a-popover>
            </template>
          </a-space>
        </template>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" name="customer-reg-list4-summary" setup>
  import { onMounted, onUnmounted, reactive, ref, watch, nextTick } from 'vue';
  import type { RangeValue } from '#/types';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns4Summary } from '/@/views/reg/CustomerReg.data';
  import { listReg, updateReportEditLockFlag } from '/@/views/summary/CustomerRegSummary.api.ts';
  import { companyTeamList } from '/@/views/reg/CompanyReg.api';
  import { useUserStore } from '/@/store/modules/user';
  import JDictSelectTag from '@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { message, theme } from 'ant-design-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { JAsyncSearchSelect } from '@/components/Form';
  import dayjs from 'dayjs';
  import { SortAscendingOutlined } from '@ant-design/icons-vue';
  import { getRegById } from '@/views/summary/Summary.api';
  import { querySysParamByCode } from '@/views/basicinfo/SysSetting.api';
  import _ from 'lodash';

  const toggleSearchStatus = ref<boolean>(false);
  const userStore = useUserStore();
  const { token } = theme.useToken();
  const { createConfirm, notification } = useMessage();
  const addCurrentUser2QueryParma = ref<string>('0');
  const emit = defineEmits(['rowClick', 'readIdcard', 'add', 'batchRegOk', 'reloadRecord']);
  const formRef = ref();
  const examNoInput = ref();
  const scanInput = ref();

  // 扫码专用输入值
  const scanInputValue = ref('');

  // 扫码相关状态
  const scanStatus = reactive({
    isScanning: false,
    message: '',
    lastScanTime: 0,
    scanBuffer: '',
    scanTimeout: null as NodeJS.Timeout | null,
    inputFocused: false,
  });

  // 扫码配置
  const scanConfig = {
    minLength: 3, // 最小扫码长度
    maxLength: 50, // 最大扫码长度
    scanInterval: 100, // 扫码间隔时间(ms)，用于识别连续输入
    searchDelay: 300, // 搜索延迟时间(ms)
    highlightDuration: 2000, // 高亮持续时间(ms)
    debounceDelay: 500, // 防抖延迟时间(ms)
  };
  const queryParam = reactive<any>({
    status: '已登记',
    sortOrder: '降序',
    dateType: '登记日期',
    doctorType: '指定的主检',
    //doctor: userStore.getUserInfo.username ?? null,
  });
  //根据addCurrentUser2QueryParma判断是否添加当前用户到查询条件
  watch(
    () => addCurrentUser2QueryParma.value,
    (newVal) => {
      if (newVal == '1') {
        queryParam.doctor = userStore.getUserInfo.username ?? null;
      } else {
        queryParam.doctor = null;
      }
    }
  );

  const regDateRange = ref<RangeValue>();
  const rangePresets = ref([
    { label: '今天', value: [dayjs(), dayjs().add(1, 'd')] },
    { label: '过去一周', value: [dayjs().add(-7, 'd'), dayjs()] },
    { label: '过去二周', value: [dayjs().add(-14, 'd'), dayjs()] },
    { label: '过去30天', value: [dayjs().add(-30, 'd'), dayjs()] },
    { label: '过去90天', value: [dayjs().add(-90, 'd'), dayjs()] },
    { label: '过去一年', value: [dayjs().add(-1, 'y'), dayjs()] },
    { label: '过去两年', value: [dayjs().add(-2, 'y'), dayjs()] },
    { label: '过去二十年', value: [dayjs().add(-20, 'y'), dayjs()] },
  ]);

  /**表格相关操作*/
  const currentRow = ref<any>({});
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      showTableSetting: false,
      showIndexColumn: true,
      api: listReg,
      columns: columns4Summary,
      canResize: true,
      canColDrag: true,
      useSearchForm: false,
      clickToRowSelect: false,
      size: 'small',
      striped: true,
      actionColumn: {
        width: 80,
        fixed: 'right',
      },
      pagination: {
        pageSize: 15,
      },
      customRow: (record) => {
        return {
          onDblclick: () => {
            currentRow.value = record;
            emit('rowClick', record);
          },
        };
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
      afterFetch: (dataSource) => {
        if (currentRow.value && dataSource.length > 0) {
          let record = dataSource.find((item) => item.id === currentRow.value.id);
          if (record) {
            currentRow.value = record;
            emit('reloadRecord', record);
          }
        }
        return dataSource;
      },
      rowClassName: (record) => {
        return currentRow.value && currentRow.value.id === record.id ? 'row-selected' : '';
      },
    },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }] = tableContext;

  const teamList = ref<any[]>([]);
  const labelCol = reactive({
    span: 7,
  });
  const wrapperCol = reactive({
    span: 17,
  });

  function getTeamList(companyRegId) {
    if (!companyRegId) {
      teamList.value = [];
      queryParam.teamId = '';
      return;
    }
    teamList.value = [];
    queryParam.teamId = '';
    companyTeamList({ companyRegId: companyRegId, pageSize: 10000 }).then((res) => {
      teamList.value = res.records;
    });
  }

  /**
   * 查询
   */
  async function searchQuery() {
    if (regDateRange.value) {
      queryParam.dateStart = regDateRange.value[0].format('YYYY-MM-DD') + ' 00:00:00';
      queryParam.dateEnd = regDateRange.value[1].format('YYYY-MM-DD') + ' 23:59:59';
    }
    await reload({ page: 1 });
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    //刷新数据
    reload();
  }

  function reloadPage() {
    reload();
  }

  function reloadCurrent() {
    if (currentRow.value.id) {
      getRegById({ regId: currentRow.value.id }).then((record) => {
        //console.log('reloadCurrent', record);
        Object.assign(currentRow.value, record);
        //currentRow.value = record;
        emit('rowClick', record);
      });
    }
  }

  // 扫码相关函数
  /**
   * 处理扫码输入
   */
  const handleScanInput = _.debounce((value: string) => {
    const now = Date.now();

    // 如果输入间隔很短且输入框有焦点，认为是扫码枪输入
    if (scanStatus.inputFocused && value && value.length >= scanConfig.minLength) {
      const inputSpeed = value.length / Math.max(now - scanStatus.lastScanTime, 1);

      // 如果输入速度很快（每毫秒超过0.1个字符），认为是扫码枪
      if (inputSpeed > 0.1) {
        scanStatus.isScanning = true;
        scanStatus.message = '检测到扫码输入，正在处理...';

        // 延迟处理，确保扫码完成
        setTimeout(() => {
          handleScanComplete(value);
        }, scanConfig.searchDelay);
      }
    }

    scanStatus.lastScanTime = now;
  }, scanConfig.debounceDelay);

  /**
   * 处理体检号输入（兼容旧版本）
   */
  function handleExamNoInput(value: string) {
    // 同步到扫码输入框
    scanInputValue.value = value;
    handleScanInput(value);
  }

  /**
   * 处理扫码完成
   */
  function handleScanComplete(scannedValue: string) {
    console.log('扫码完成:', scannedValue);

    if (!scannedValue || scannedValue.length < scanConfig.minLength) {
      resetScanStatus();
      return;
    }

    scanStatus.message = `扫码成功: ${scannedValue}，正在搜索...`;

    // 延迟执行搜索，给用户反馈时间
    setTimeout(() => {
      performScanSearch(scannedValue);
    }, scanConfig.searchDelay);
  }

  /**
   * 执行扫码搜索
   */
  async function performScanSearch(examNo: string) {
    try {
      scanStatus.message = `正在搜索体检号: ${examNo}...`;

      // 设置查询参数
      queryParam.examNo = examNo;
      scanInputValue.value = examNo;

      // 执行搜索
      await searchQuery();

      // 搜索完成后，尝试自动选中匹配的记录
      await nextTick();
      autoSelectScannedRecord(examNo);

    } catch (error) {
      console.error('扫码搜索失败:', error);
      message.error('扫码搜索失败，请重试');
      scanStatus.message = '搜索失败，请重试';
    } finally {
      setTimeout(() => {
        resetScanStatus();
      }, 2000); // 延迟重置，让用户看到结果
    }
  }

  /**
   * 自动选中扫码的记录
   */
  function autoSelectScannedRecord(examNo: string) {
    const dataSource = getDataSource();
    if (!dataSource || dataSource.length === 0) {
      message.warning(`未找到体检号为 ${examNo} 的记录`);
      return;
    }

    // 查找完全匹配的记录
    const exactMatch = dataSource.find(record => record.examNo === examNo);
    if (exactMatch) {
      currentRow.value = exactMatch;
      emit('rowClick', exactMatch);
      message.success(`已自动选中体检号: ${examNo}`);

      // 高亮显示选中的记录
      highlightSelectedRow();
      return;
    }

    // 如果没有完全匹配，选择第一条记录
    if (dataSource.length === 1) {
      currentRow.value = dataSource[0];
      emit('rowClick', dataSource[0]);
      message.info(`已选中相似记录: ${dataSource[0].examNo}`);
      highlightSelectedRow();
    } else {
      message.info(`找到 ${dataSource.length} 条相关记录，请手动选择`);
    }
  }

  /**
   * 高亮显示选中的记录
   */
  function highlightSelectedRow() {
    // 通过CSS类名高亮显示，持续一段时间后自动移除
    setTimeout(() => {
      // 这里可以添加高亮效果的移除逻辑
    }, scanConfig.highlightDuration);
  }

  /**
   * 处理手动搜索
   */
  function handleManualSearch() {
    if (scanInputValue.value && scanInputValue.value.trim()) {
      queryParam.examNo = scanInputValue.value.trim();
      performScanSearch(scanInputValue.value.trim());
    }
  }

  /**
   * 处理扫码输入框焦点
   */
  function handleScanInputFocus() {
    scanStatus.inputFocused = true;
    scanStatus.lastScanTime = Date.now();
  }

  /**
   * 处理扫码输入框失焦
   */
  function handleScanInputBlur() {
    scanStatus.inputFocused = false;
    // 延迟重置状态，避免快速焦点切换时误重置
    setTimeout(() => {
      if (!scanStatus.inputFocused) {
        resetScanStatus();
      }
    }, 200);
  }

  /**
   * 清空扫码输入
   */
  function clearScanInput() {
    scanInputValue.value = '';
    queryParam.examNo = '';
    resetScanStatus();
    // 重新聚焦到输入框
    nextTick(() => {
      scanInput.value?.focus();
    });
  }

  /**
   * 重置扫码状态
   */
  function resetScanStatus() {
    scanStatus.isScanning = false;
    scanStatus.message = '';
    scanStatus.scanBuffer = '';
    if (scanStatus.scanTimeout) {
      clearTimeout(scanStatus.scanTimeout);
      scanStatus.scanTimeout = null;
    }
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    if (record.reportEditLockFlag == '0') {
      updateReportEditLockFlag({ customerRegId: record.id, reportEditLockFlag: '1' }).then((res) => {
        if (res.success) {
          record.reportEditLockFlag = '1';
          let user = userStore.getUserInfo;
          record.reportEditLockBy = user.username;
          record.reportEditLocker = user.realname;
          record.reportEditLockTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
          emit('rowClick', record);
        } else {
          message.error(res.message);
        }
      });
    } else {
      message.error('该记录已被锁定，无法编辑！');
    }
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    let action = [];
    if (record.reportEditLockFlag == '1') {
      action.push({
        label: '已锁',
        onClick: null,
      });
    } else {
      if (!(record.summaryStatus == '审核通过' || record.summaryStatus == '驳回')) {
        action.push({
          label: '锁定',
          onClick: handleEdit.bind(null, record),
        });
      }
    }
    return action;
  }

  /**
   * 全局键盘事件监听（用于扫码枪）
   */
  function handleGlobalKeydown(event: KeyboardEvent) {
    // 如果焦点在输入框中，不处理全局扫码
    if (event.target && (event.target as HTMLElement).tagName === 'INPUT') {
      return;
    }

    // 检测扫码枪输入模式
    const now = Date.now();
    if (now - scanStatus.lastScanTime < scanConfig.scanInterval) {
      // 连续快速输入，可能是扫码枪
      if (event.key.length === 1) { // 普通字符
        scanStatus.scanBuffer += event.key;
        scanStatus.isScanning = true;
        scanStatus.message = '正在扫码...';

        // 清除之前的超时
        if (scanStatus.scanTimeout) {
          clearTimeout(scanStatus.scanTimeout);
        }

        // 设置扫码完成超时
        scanStatus.scanTimeout = setTimeout(() => {
          handleScanComplete(scanStatus.scanBuffer);
          scanStatus.scanBuffer = '';
        }, scanConfig.scanInterval * 3);
      } else if (event.key === 'Enter' && scanStatus.scanBuffer) {
        // 扫码枪通常以回车结束
        event.preventDefault();
        if (scanStatus.scanTimeout) {
          clearTimeout(scanStatus.scanTimeout);
        }
        handleScanComplete(scanStatus.scanBuffer);
        scanStatus.scanBuffer = '';
      }
    } else {
      // 重新开始扫码检测
      if (event.key.length === 1) {
        scanStatus.scanBuffer = event.key;
      }
    }

    scanStatus.lastScanTime = now;
  }

  onMounted(() => {
    const savedSortOrder = localStorage.getItem('summaryRegDateType');
    queryParam.dateType = savedSortOrder || '降序';

    querySysParamByCode({ code: 'summary_list_condition_current_user' }).then((res) => {
      addCurrentUser2QueryParma.value = res.result;
    });

    // 添加全局键盘事件监听
    document.addEventListener('keydown', handleGlobalKeydown);
  });

  onUnmounted(() => {
    // 清理事件监听器
    document.removeEventListener('keydown', handleGlobalKeydown);

    // 清理扫码状态
    resetScanStatus();
  });

  watch(
    () => queryParam.dateType,
    (newSortOrder) => {
      localStorage.setItem('summaryRegDateType', newSortOrder);
    }
  );

  defineExpose({
    searchQuery,
    reloadPage,
    reloadCurrent,
  });
</script>

<style lang="less" scoped>
  // 扫码输入区域样式
  .scan-input-section {
    margin-bottom: 12px;

    .scan-card {
      border: 2px solid #e8f4fd;
      border-radius: 8px;
      background: linear-gradient(135deg, #f8fcff 0%, #e8f4fd 100%);
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);

      .ant-card-head {
        background: transparent;
        border-bottom: 1px solid #d6e4ff;
      }
    }

    .scan-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #1890ff;

      .scan-icon {
        font-size: 16px;
      }

      .scan-status-tag {
        margin-left: auto;
      }
    }

    .scan-input-wrapper {
      .scan-prefix-icon {
        color: #1890ff;
        font-size: 16px;
      }

      .clear-btn {
        color: #999;
        &:hover {
          color: #ff4d4f;
        }
      }

      .scan-message {
        margin-top: 8px;
        padding: 4px 8px;
        background: #f0f9ff;
        border: 1px solid #d6e4ff;
        border-radius: 4px;
        color: #1890ff;
        font-size: 12px;
        text-align: center;
      }
    }
  }

  // 扫码输入框激活状态
  .scan-input-active {
    border-color: #52c41a !important;
    box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2) !important;
    animation: scanInputGlow 1.5s infinite alternate;
  }

  // 扫码动画
  @keyframes scanInputGlow {
    0% {
      box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
    }
    100% {
      box-shadow: 0 0 0 4px rgba(82, 196, 26, 0.4);
    }
  }

  .jeecg-basic-table-form-container {
    padding: 0;

    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 8px;
      white-space: nowrap;
    }

    .query-group-cust {
      min-width: 100px !important;
    }

    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
  }

  :deep(.row-selected td:first-child) {
    border-left: solid 5px v-bind('token.colorPrimary');
  }

  // 扫码高亮行样式
  :deep(.row-scan-highlight) {
    background-color: rgba(82, 196, 26, 0.1) !important;
    animation: rowHighlight 2s ease-out;

    td {
      border-color: #52c41a !important;
    }
  }

  @keyframes rowHighlight {
    0% {
      background-color: rgba(82, 196, 26, 0.3);
    }
    100% {
      background-color: rgba(82, 196, 26, 0.1);
    }
  }
</style>
