# 职业病总检扫码功能测试指南

## 功能概述

职业病总检专用组件已经集成了扫码枪功能，支持快速扫描体检号进行人员检索和自动选中。

## 扫码功能特性

### 1. 智能扫码识别
- **自动检测扫码枪输入**：系统能够识别连续快速输入模式
- **防抖处理**：避免误触发，提高准确性
- **输入缓冲**：支持不同速度的扫码枪设备

### 2. 用户界面优化
- **专用扫码输入区域**：顶部显著位置的扫码输入框
- **实时状态提示**：扫码状态、搜索进度的可视化反馈
- **扫码动画效果**：输入框高亮和动画提示

### 3. 自动化流程
- **自动搜索**：扫码完成后自动触发搜索
- **自动选中**：找到匹配记录后自动选中并切换到总检面板
- **智能匹配**：支持完全匹配和模糊匹配

## 测试步骤

### 测试环境准备
1. 启动前端开发服务器：`npm run dev`
2. 访问：http://localhost:3200
3. 导航到职业病总检页面

### 扫码枪兼容性测试

#### 测试用例1：标准扫码枪测试
**测试目标**：验证标准USB扫码枪的兼容性

**测试步骤**：
1. 连接USB扫码枪到电脑
2. 打开职业病总检页面
3. 点击扫码输入框使其获得焦点
4. 使用扫码枪扫描体检号条码
5. 观察系统响应

**预期结果**：
- 扫码输入框显示扫码状态（蓝色高亮+动画）
- 状态标签显示"扫码中..."
- 扫码完成后自动搜索
- 找到记录后自动选中并切换到总检面板

#### 测试用例2：无线扫码枪测试
**测试目标**：验证无线扫码枪的兼容性

**测试步骤**：
1. 配对无线扫码枪
2. 重复测试用例1的步骤

**预期结果**：与有线扫码枪相同

#### 测试用例3：手机扫码APP测试
**测试目标**：验证手机扫码应用的兼容性

**测试步骤**：
1. 使用手机扫码应用扫描条码
2. 通过输入法输入到扫码框
3. 观察系统响应

### 输入准确性测试

#### 测试用例4：不同长度体检号测试
**测试数据**：
- 短体检号：123
- 标准体检号：TJ20240826001
- 长体检号：HOSPITAL-TJ-2024-08-26-001

**测试步骤**：
1. 分别扫描不同长度的体检号
2. 验证系统识别和搜索功能

**预期结果**：
- 长度≥3的体检号能正常识别
- 搜索功能正常工作
- 匹配结果正确显示

#### 测试用例5：特殊字符测试
**测试数据**：包含特殊字符的体检号
- TJ-2024/08/26-001
- TJ_2024.08.26_001
- TJ@2024#08#26@001

**预期结果**：特殊字符能正确识别和搜索

### 响应速度测试

#### 测试用例6：扫码速度测试
**测试目标**：验证不同扫码速度的响应

**测试步骤**：
1. 快速连续扫码（间隔<100ms）
2. 正常速度扫码（间隔100-500ms）
3. 慢速扫码（间隔>500ms）

**预期结果**：
- 快速扫码：识别为扫码枪输入，自动搜索
- 正常速度：正常处理
- 慢速扫码：识别为手动输入

### 异常情况测试

#### 测试用例7：网络延迟测试
**测试步骤**：
1. 模拟网络延迟（开发者工具 > Network > Slow 3G）
2. 执行扫码操作
3. 观察系统响应

**预期结果**：
- 显示"正在搜索..."状态
- 网络恢复后正常显示结果
- 超时后显示错误提示

#### 测试用例8：无匹配结果测试
**测试步骤**：
1. 扫描不存在的体检号
2. 观察系统响应

**预期结果**：
- 显示"未找到匹配记录"提示
- 输入框保持输入值
- 用户可以手动修改后重新搜索

#### 测试用例9：扫码失败测试
**测试步骤**：
1. 扫描模糊或损坏的条码
2. 观察系统响应

**预期结果**：
- 系统不会误识别
- 用户可以重新扫码或手动输入

### 用户体验测试

#### 测试用例10：多次扫码测试
**测试步骤**：
1. 连续扫描多个不同的体检号
2. 验证每次切换的流畅性

**预期结果**：
- 每次扫码都能正确切换人员
- 界面响应流畅
- 数据加载正确

#### 测试用例11：手动输入兼容性测试
**测试步骤**：
1. 手动在扫码框中输入体检号
2. 按回车键搜索
3. 验证功能正常

**预期结果**：
- 手动输入功能正常
- 搜索结果正确
- 与扫码功能无冲突

## 性能指标

### 响应时间要求
- 扫码识别延迟：< 200ms
- 搜索响应时间：< 1s（正常网络）
- 界面切换时间：< 500ms

### 准确性要求
- 扫码识别准确率：> 99%
- 搜索匹配准确率：100%
- 自动选中准确率：100%

## 常见问题排查

### 问题1：扫码枪无响应
**可能原因**：
- 扫码枪未正确连接
- 输入框未获得焦点
- 浏览器兼容性问题

**解决方案**：
- 检查设备连接
- 点击输入框获得焦点
- 尝试不同浏览器

### 问题2：扫码识别错误
**可能原因**：
- 条码质量差
- 扫码速度过快或过慢
- 输入法干扰

**解决方案**：
- 使用清晰的条码
- 调整扫码速度
- 切换到英文输入法

### 问题3：搜索无结果
**可能原因**：
- 体检号不存在
- 网络连接问题
- 后端服务异常

**解决方案**：
- 确认体检号正确
- 检查网络连接
- 联系技术支持

## 浏览器兼容性

### 支持的浏览器
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### 不支持的浏览器
- Internet Explorer（所有版本）
- Chrome < 80
- Firefox < 75

## 技术实现说明

### 扫码识别原理
1. **键盘事件监听**：监听全局和输入框的键盘事件
2. **输入速度检测**：计算字符输入速度判断是否为扫码枪
3. **防抖处理**：使用lodash.debounce避免频繁触发
4. **状态管理**：使用Vue3响应式系统管理扫码状态

### 关键配置参数
```javascript
const scanConfig = {
  minLength: 3,           // 最小扫码长度
  maxLength: 50,          // 最大扫码长度
  scanInterval: 100,      // 扫码间隔时间(ms)
  searchDelay: 300,       // 搜索延迟时间(ms)
  debounceDelay: 500,     // 防抖延迟时间(ms)
};
```

## 更新日志

### v1.0.0 (2024-08-26)
- 初始版本发布
- 支持基本扫码功能
- 集成自动搜索和选中
- 添加用户界面优化
