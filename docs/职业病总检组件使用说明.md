# 职业病总检组件使用说明

## 组件概述

`SummaryPannel4Occu.vue` 是专门为职业病总检设计的专用组件，提供了扫码枪支持、职业检结论管理等功能。

## 主要特性

### 1. 扫码枪支持
- **智能识别**：自动识别扫码枪输入模式
- **实时反馈**：扫码状态可视化提示
- **自动搜索**：扫码完成后自动检索人员信息
- **自动选中**：找到匹配记录后自动选中并切换界面

### 2. 职业检专用功能
- **快速模板**：提供常用职业检结论模板
- **批量操作**：支持批量编辑、保存、删除
- **数据导出**：支持导出职业检结论数据
- **键盘快捷键**：提高操作效率

### 3. 优化的用户界面
- **左右分栏布局**：左侧人员列表，右侧职业检面板
- **响应式设计**：适配不同屏幕尺寸
- **专业视觉标识**：职业检专用的颜色和图标

## 组件结构

```
SummaryPannel4Occu.vue
├── 左侧面板 (30% 宽度)
│   ├── 扫码输入区域
│   │   ├── 扫码状态指示器
│   │   ├── 体检号输入框
│   │   └── 状态提示信息
│   └── CustomerRegList4Summary
│       ├── 扫码监听器
│       ├── 人员列表
│       └── 搜索功能
└── 右侧面板 (70% 宽度)
    ├── 患者信息卡片
    ├── 项目状态概览
    └── 职业检结论面板
        ├── ZyConclusionDetailList
        ├── 快捷操作工具栏
        └── 批量操作功能
```

## 使用方法

### 基本使用

```vue
<template>
  <SummaryPannel4Occu />
</template>

<script setup>
import SummaryPannel4Occu from '@/views/summary/SummaryPannel4Occu.vue';
</script>
```

### 扫码功能使用

1. **准备工作**
   - 确保扫码枪正确连接
   - 打开职业病总检页面
   - 点击扫码输入框获得焦点

2. **扫码操作**
   - 使用扫码枪扫描体检号条码
   - 系统自动识别并搜索
   - 找到记录后自动选中

3. **手动输入**
   - 在扫码输入框中手动输入体检号
   - 按回车键或点击搜索按钮
   - 系统执行搜索并显示结果

### 职业检结论管理

1. **新增结论**
   - 点击"新增职业检结论"按钮
   - 填写结论信息
   - 点击保存

2. **使用快速模板**
   - 点击"快速模板"下拉菜单
   - 选择合适的模板类型
   - 系统自动填充模板内容
   - 根据需要修改后保存

3. **批量操作**
   - 选中多个结论记录
   - 使用批量操作菜单
   - 支持批量删除、导出等功能

## 配置选项

### 扫码配置

```javascript
const scanConfig = {
  minLength: 3,           // 最小扫码长度
  maxLength: 50,          // 最大扫码长度
  scanInterval: 100,      // 扫码间隔时间(ms)
  searchDelay: 300,       // 搜索延迟时间(ms)
  debounceDelay: 500,     // 防抖延迟时间(ms)
  highlightDuration: 2000 // 高亮持续时间(ms)
};
```

### 快速模板配置

```javascript
const templates = {
  normal: {
    conclusionType: '正常',
    conclusionContent: '各项检查结果均在正常范围内...',
    suggestion: '继续从事现工作，定期体检。',
    riskLevel: '低风险'
  },
  abnormal: {
    conclusionType: '异常',
    conclusionContent: '检查发现异常指标...',
    suggestion: '建议进一步检查...',
    riskLevel: '中风险'
  }
  // ... 更多模板
};
```

## API 接口

### 主要接口

1. **人员搜索接口**
   ```javascript
   // 根据体检号搜索人员
   listReg(queryParam)
   ```

2. **职业检结论接口**
   ```javascript
   // 获取职业检结论列表
   list(params)
   
   // 保存或更新结论
   saveOrUpdate(data)
   
   // 删除结论
   deleteOne(id)
   
   // 批量删除
   batchDelete(ids)
   ```

3. **审核相关接口**
   ```javascript
   // 审核职业检结论
   auditSummary(data)
   
   // 撤销审核
   revokeSummaryStatus(data)
   ```

## 事件说明

### 组件事件

```javascript
// 人员选中事件
emit('rowClick', selectedRow)

// 扫码完成事件
emit('scanComplete', scannedValue)

// 数据保存事件
emit('dataSaved', savedData)
```

### 键盘快捷键

- `Ctrl + S`：保存当前编辑的结论
- `Ctrl + N`：新增结论
- `Escape`：取消当前编辑
- `F5`：刷新数据

## 样式定制

### CSS 变量

```css
:root {
  --occu-primary-color: #1890ff;
  --occu-success-color: #52c41a;
  --occu-warning-color: #faad14;
  --occu-error-color: #ff4d4f;
}
```

### 主要样式类

```css
.occu-summary-title     /* 标题样式 */
.occu-toolbar          /* 工具栏样式 */
.scan-input-section    /* 扫码输入区域 */
.scan-input-active     /* 扫码激活状态 */
.conclusion-cards      /* 结论卡片容器 */
```

## 权限控制

### 权限点说明

```javascript
// 总检权限
'summary:customer_reg_summary:add'    // 新增总检
'summary:customer_reg_summary:edit'   // 编辑总检
'summary:customer_reg_summary:delete' // 删除总检

// 审核权限
'summary:summary_audit_record:add'    // 审核权限

// 打印权限
'summary:report:print'                // 报告打印
```

### 权限检查

```javascript
import { usePermission } from '/@/hooks/web/usePermission';

const { hasPermission } = usePermission();

// 检查权限
if (hasPermission('summary:customer_reg_summary:add')) {
  // 显示新增按钮
}
```

## 错误处理

### 常见错误及解决方案

1. **扫码无响应**
   - 检查扫码枪连接
   - 确认输入框焦点
   - 检查浏览器兼容性

2. **搜索无结果**
   - 确认体检号正确
   - 检查网络连接
   - 验证后端服务状态

3. **保存失败**
   - 检查必填字段
   - 验证数据格式
   - 确认用户权限

### 错误日志

```javascript
// 启用调试模式
localStorage.setItem('debug', 'true');

// 查看控制台日志
console.log('扫码状态:', scanStatus);
console.log('搜索参数:', queryParam);
```

## 性能优化

### 优化建议

1. **数据加载优化**
   - 使用分页加载
   - 实现虚拟滚动
   - 缓存常用数据

2. **扫码性能优化**
   - 防抖处理
   - 输入验证
   - 状态管理优化

3. **界面渲染优化**
   - 使用v-memo缓存
   - 组件懒加载
   - 图片懒加载

## 浏览器兼容性

### 支持的浏览器
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### 已知问题
- IE浏览器不支持
- 部分移动端浏览器扫码功能受限

## 更新日志

### v1.0.0 (2024-08-26)
- 初始版本发布
- 支持扫码枪功能
- 职业检专用界面
- 快速模板功能
- 批量操作支持

## 技术支持

如有问题，请联系：
- 技术支持邮箱：<EMAIL>
- 开发团队：<EMAIL>
- 文档更新：<EMAIL>
